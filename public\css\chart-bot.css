/* Chart GPT Style CSS */

/* Reset and Base Styles */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  background-color: #ffffff;
  color: #333333;
  line-height: 1.6;
}

/* Chart GPT Container */
.chartgpt-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Header */
.chartgpt-header {
  padding: 20px 0;
  border-bottom: 1px solid #e5e5e5;
  margin-bottom: 40px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.nav-links {
  display: flex;
  gap: 30px;
}

.nav-link {
  display: flex;
  align-items: center;
  gap: 8px;
  text-decoration: none;
  color: #666666;
  font-weight: 500;
  padding: 8px 16px;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.nav-link:hover {
  color: #333333;
  background-color: #f5f5f5;
}

.nav-link.active {
  color: #000000;
  background-color: #f0f0f0;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.btn-minimal {
  background: none;
  border: none;
  padding: 8px;
  border-radius: 6px;
  color: #666666;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-minimal:hover {
  background-color: #f5f5f5;
  color: #333333;
}

.bot-message .message-avatar {
  background-color: #2196F3;
  color: white;
  order: 1;
}

.message-avatar img {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
}

.message-content {
  max-width: 70%;
  padding: 0.75rem 1rem;
  border-radius: 18px;
  position: relative;
}

.user-message .message-content {
  background-color: #4CAF50;
  color: white;
  border-bottom-right-radius: 4px;
  order: 1;
}

.bot-message .message-content {
  background-color: #f1f3f4;
  color: #333;
  border-bottom-left-radius: 4px;
  order: 2;
}

.message-text {
  margin-bottom: 0.25rem;
  line-height: 1.4;
}

.message-time {
  font-size: 0.75rem;
  opacity: 0.7;
  margin-top: 0.25rem;
}

.message-sources {
  margin-top: 0.5rem;
  padding-top: 0.5rem;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.message-sources .badge {
  font-size: 0.7rem;
  margin: 0.1rem;
}

/* Enhanced source display styles */
.vector-store-indicator {
  margin-bottom: 0.5rem;
  padding: 0.25rem 0.5rem;
  background-color: #f8f9fa;
  border-left: 3px solid #28a745;
  border-radius: 0.25rem;
}

.message-sources-enhanced {
  margin-top: 0.75rem;
  padding: 0.75rem;
  background-color: #f8f9fa;
  border-radius: 0.5rem;
  border: 1px solid #e9ecef;
}

.sources-header {
  margin-bottom: 0.5rem;
  font-weight: 600;
}

.sources-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.source-item {
  display: flex;
  align-items: flex-start;
  gap: 0.5rem;
  padding: 0.5rem;
  background-color: white;
  border-radius: 0.25rem;
  border: 1px solid #dee2e6;
}

.source-badge .badge {
  font-size: 0.75rem;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.source-details {
  flex: 1;
}

.source-title {
  font-weight: 500;
  font-size: 0.875rem;
  color: #495057;
  margin-bottom: 0.25rem;
}

.source-meta {
  font-size: 0.75rem;
}

.message-status {
  margin-top: 0.5rem;
  padding: 0.25rem 0.5rem;
  background-color: #f8f9fa;
  border-radius: 0.25rem;
  border-left: 3px solid #6c757d;
}

.message-status .text-success {
  border-left-color: #28a745;
}

.message-status .text-danger {
  border-left-color: #dc3545;
}

.message-status .text-warning {
  border-left-color: #ffc107;
}

/* Processing message styles */
.processing-message {
  opacity: 0.8;
}

.processing-message .message-content {
  background-color: #f8f9fa;
  border: 1px dashed #dee2e6;
  border-radius: 0.5rem;
  padding: 0.75rem;
}

.processing-message .vector-store-indicator {
  background-color: transparent;
  border: none;
  padding: 0;
  margin-bottom: 0.5rem;
}

.processing-message .spinner-border-sm {
  width: 1rem;
  height: 1rem;
  margin-right: 0.5rem;
}

/* Chat Input Styles */
.chat-input-container {
  background-color: #ffffff;
  padding: 1rem;
  border: 1px solid #e9ecef;
  border-radius: 8px;
}

.chat-suggestions {
  margin-top: 0.5rem;
}

.chat-suggestions .btn {
  margin: 0.2rem;
  font-size: 0.8rem;
}

/* Status Indicator */
.chart-bot-status {
  display: flex;
  align-items: center;
}

.chart-bot-status .badge {
  font-size: 0.75rem;
}

/* Chart Modal Styles */
.modal-xl {
  max-width: 90%;
}

#chart-container {
  position: relative;
  width: 100%;
  height: 400px;
}

#chart-canvas {
  max-width: 100%;
  max-height: 100%;
}

/* Saved Charts Styles */
.saved-chart-item {
  padding: 0.75rem;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  margin-bottom: 0.5rem;
  background-color: #ffffff;
  transition: all 0.2s ease;
}

.saved-chart-item:hover {
  border-color: #4CAF50;
  box-shadow: 0 2px 4px rgba(76, 175, 80, 0.1);
}

.saved-chart-item h6 {
  color: #333;
  font-size: 0.9rem;
}

.saved-chart-item small {
  font-size: 0.75rem;
}

/* Quick Actions */
.fb-card .btn-sm {
  padding: 0.5rem 0.75rem;
  font-size: 0.85rem;
}

/* Loading Animation */
.spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .chat-messages-container {
    height: 400px;
  }
  
  .message-content {
    max-width: 85%;
  }
  
  .modal-xl {
    max-width: 95%;
  }
  
  .chat-suggestions .btn {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
  }
}

@media (max-width: 576px) {
  .chat-messages-container {
    height: 350px;
    padding: 0.5rem;
  }
  
  .message-content {
    max-width: 90%;
    padding: 0.5rem 0.75rem;
  }
  
  .message-avatar {
    width: 32px;
    height: 32px;
  }
  
  .chat-input-container {
    padding: 0.75rem;
  }
  
  .saved-chart-item {
    padding: 0.5rem;
  }
}

/* Input Area */
.input-area {
  width: 100%;
  max-width: 600px;
  margin: 0 auto;
}

.input-container {
  position: relative;
  display: flex;
  align-items: center;
  background-color: #ffffff;
  border: 1px solid #d1d5db;
  border-radius: 12px;
  padding: 4px;
  margin-bottom: 16px;
  transition: all 0.2s ease;
}

.input-container:focus-within {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.chat-input {
  flex: 1;
  border: none;
  outline: none;
  padding: 12px 16px;
  font-size: 1rem;
  background: transparent;
  color: #111827;
}

.chat-input::placeholder {
  color: #9ca3af;
}

.send-button {
  background-color: #000000;
  color: #ffffff;
  border: none;
  border-radius: 8px;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.send-button:hover {
  background-color: #1f2937;
}

.send-button:disabled {
  background-color: #d1d5db;
  cursor: not-allowed;
}

/* Suggestions */
.suggestions {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  justify-content: center;
}

.suggestion-btn {
  background-color: #f9fafb;
  border: 1px solid #e5e7eb;
  color: #374151;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.suggestion-btn:hover {
  background-color: #f3f4f6;
  border-color: #d1d5db;
}

.suggestion-btn.lucky-btn {
  background-color: #3b82f6;
  color: #ffffff;
  border-color: #3b82f6;
}

.suggestion-btn.lucky-btn:hover {
  background-color: #2563eb;
}

/* Charts Sidebar */
.charts-sidebar {
  position: fixed;
  top: 0;
  right: -400px;
  width: 400px;
  height: 100vh;
  background-color: #ffffff;
  border-left: 1px solid #e5e7eb;
  z-index: 1000;
  transition: right 0.3s ease;
  overflow-y: auto;
}

.charts-sidebar.active {
  right: 0;
}

.sidebar-header {
  padding: 20px;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.sidebar-header h3 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: #111827;
}

.close-sidebar {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #6b7280;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.close-sidebar:hover {
  background-color: #f3f4f6;
  color: #374151;
}

.sidebar-content {
  padding: 20px;
}

.saved-chart-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  margin-bottom: 12px;
  transition: all 0.2s ease;
}

.saved-chart-item:hover {
  border-color: #d1d5db;
  background-color: #f9fafb;
}

.chart-info h4 {
  margin: 0 0 4px 0;
  font-size: 0.875rem;
  font-weight: 500;
  color: #111827;
}

.chart-info small {
  color: #6b7280;
  font-size: 0.75rem;
}

.chart-actions {
  display: flex;
  gap: 8px;
}

.btn-icon {
  background: none;
  border: none;
  padding: 6px;
  border-radius: 4px;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-icon:hover {
  background-color: #f3f4f6;
  color: #374151;
}

.no-charts {
  text-align: center;
  color: #6b7280;
  font-style: italic;
  margin: 40px 0;
}

/* Chart Modal */
.chart-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 2000;
  display: none;
  align-items: center;
  justify-content: center;
}

.modal-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
}

.chart-modal .modal-content {
  position: relative;
  background-color: #ffffff;
  border-radius: 12px;
  max-width: 90vw;
  max-height: 90vh;
  width: 800px;
  display: flex;
  flex-direction: column;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.chart-modal .modal-header {
  padding: 20px;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-modal .modal-header h3 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: #111827;
}

.close-modal {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #6b7280;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.close-modal:hover {
  background-color: #f3f4f6;
  color: #374151;
}

.chart-modal .modal-body {
  padding: 20px;
  flex: 1;
  overflow: auto;
}

#chart-container {
  width: 100%;
  height: 400px;
  position: relative;
}

.chart-modal .modal-footer {
  padding: 20px;
  border-top: 1px solid #e5e7eb;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.btn-secondary {
  background-color: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-secondary:hover {
  background-color: #e5e7eb;
}

.btn-primary {
  background-color: #3b82f6;
  color: #ffffff;
  border: 1px solid #3b82f6;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 6px;
}

.btn-primary:hover {
  background-color: #2563eb;
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
  .chartgpt-container {
    padding: 0 16px;
  }

  .chartgpt-title h1 {
    font-size: 2.5rem;
  }

  .nav-links {
    gap: 20px;
  }

  .nav-link span {
    display: none;
  }

  .charts-sidebar {
    width: 100%;
    right: -100%;
  }

  .suggestions {
    flex-direction: column;
    align-items: center;
  }

  .suggestion-btn {
    width: 100%;
    max-width: 300px;
    text-align: center;
  }

  .chart-modal .modal-content {
    width: 95vw;
    height: 95vh;
  }

  #chart-container {
    height: 300px;
  }
}

@media (max-width: 480px) {
  .chartgpt-title h1 {
    font-size: 2rem;
  }

  .chartgpt-title p {
    font-size: 1rem;
  }

  .header-content {
    flex-direction: column;
    gap: 16px;
  }

  .input-container {
    padding: 2px;
  }

  .chat-input {
    padding: 10px 12px;
    font-size: 0.9rem;
  }

  .send-button {
    width: 32px;
    height: 32px;
  }
}

/* Chart Bot Specific Animations */
.chart-bot-loading {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 3px solid rgba(76, 175, 80, 0.3);
  border-radius: 50%;
  border-top-color: #4CAF50;
  animation: spin 1s ease-in-out infinite;
}

/* Success/Error States */
.message-success {
  border-left: 4px solid #4CAF50;
  background-color: rgba(76, 175, 80, 0.1);
}

.message-error {
  border-left: 4px solid #f44336;
  background-color: rgba(244, 67, 54, 0.1);
}

.message-warning {
  border-left: 4px solid #ff9800;
  background-color: rgba(255, 152, 0, 0.1);
}

/* Chart Type Indicators */
.chart-type-indicator {
  display: inline-block;
  width: 12px;
  height: 12px;
  border-radius: 2px;
  margin-right: 0.5rem;
}

.chart-type-line {
  background-color: #2196F3;
}

.chart-type-bar {
  background-color: #4CAF50;
}

.chart-type-pie {
  background-color: #FF9800;
}

.chart-type-doughnut {
  background-color: #9C27B0;
}
