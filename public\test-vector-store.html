<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vector Store Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <link rel="stylesheet" href="/css/toast-notifications.css">
    <link rel="stylesheet" href="/css/chart-bot.css">
</head>
<body>
    <div class="container mt-5">
        <h1><i class="bi bi-database-check"></i> Vector Store Integration Test</h1>
        <div class="alert alert-info">
            <strong>Purpose:</strong> This page tests the vector store integration to ensure questions are properly retrieved from the agricultural knowledge base.
        </div>
        
        <div class="row">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="bi bi-chat-dots"></i> Test Vector Store Queries</h5>
                        <div id="connection-status"></div>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="test-query" class="form-label">Enter your agricultural question:</label>
                            <input type="text" class="form-control" id="test-query" 
                                   placeholder="e.g., How do I treat tomato blight?"
                                   value="How do I treat tomato blight?">
                        </div>
                        <button class="btn btn-primary" onclick="testVectorStore()">
                            <i class="bi bi-search"></i> Query Vector Store
                        </button>
                        <button class="btn btn-secondary ms-2" onclick="checkHealth()">
                            <i class="bi bi-heart-pulse"></i> Check Health
                        </button>
                    </div>
                </div>
                
                <div class="mt-4">
                    <h5>Response:</h5>
                    <div id="response-container" class="border rounded p-3 bg-light">
                        <em class="text-muted">No query sent yet...</em>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h6>Sample Questions</h6>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <button class="btn btn-outline-primary btn-sm" onclick="setQuery('How do I treat tomato blight?')">
                                Tomato Blight Treatment
                            </button>
                            <button class="btn btn-outline-primary btn-sm" onclick="setQuery('What are the best organic fertilizers?')">
                                Organic Fertilizers
                            </button>
                            <button class="btn btn-outline-primary btn-sm" onclick="setQuery('How to control aphids naturally?')">
                                Aphid Control
                            </button>
                            <button class="btn btn-outline-primary btn-sm" onclick="setQuery('What is crop rotation?')">
                                Crop Rotation
                            </button>
                            <button class="btn btn-outline-primary btn-sm" onclick="setQuery('How to improve soil health?')">
                                Soil Health
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="card mt-3">
                    <div class="card-header">
                        <h6>Test Results</h6>
                    </div>
                    <div class="card-body">
                        <div id="test-results">
                            <em class="text-muted">No tests run yet...</em>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/js/toast-notifications.js"></script>

    <script>
        let testCount = 0;
        
        function setQuery(query) {
            document.getElementById('test-query').value = query;
        }
        
        async function checkHealth() {
            try {
                const response = await fetch('/chart-bot/api/health');
                const data = await response.json();
                
                const statusDiv = document.getElementById('connection-status');
                
                if (data.success && data.chartBotStatus === 'healthy') {
                    statusDiv.innerHTML = `
                        <span class="badge bg-success">
                            <i class="bi bi-database-check"></i> Vector Store Online
                        </span>
                    `;
                    toast.success('Vector store is online and ready!');
                } else {
                    statusDiv.innerHTML = `
                        <span class="badge bg-warning">
                            <i class="bi bi-database-exclamation"></i> Limited Access
                        </span>
                    `;
                    toast.warning('Vector store access is limited');
                }
            } catch (error) {
                const statusDiv = document.getElementById('connection-status');
                statusDiv.innerHTML = `
                    <span class="badge bg-danger">
                        <i class="bi bi-database-x"></i> Vector Store Offline
                    </span>
                `;
                toast.error('Cannot connect to vector store');
            }
        }
        
        async function testVectorStore() {
            const query = document.getElementById('test-query').value.trim();
            if (!query) {
                toast.warning('Please enter a question');
                return;
            }
            
            testCount++;
            const responseContainer = document.getElementById('response-container');
            responseContainer.innerHTML = `
                <div class="text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Querying vector store...</span>
                    </div>
                    <div class="mt-2">Retrieving from agricultural knowledge base...</div>
                </div>
            `;
            
            try {
                const response = await fetch('/chart-bot/api/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        query: query,
                        maxSources: 5
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    displayResponse(data);
                    updateTestResults(testCount, query, data);
                    toast.success(`Retrieved from ${data.sources?.length || 0} knowledge sources`);
                } else {
                    responseContainer.innerHTML = `
                        <div class="alert alert-danger">
                            <i class="bi bi-exclamation-triangle"></i>
                            Error: ${data.error || 'Unknown error'}
                        </div>
                    `;
                    toast.error('Query failed');
                }
            } catch (error) {
                responseContainer.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="bi bi-wifi-off"></i>
                        Connection Error: ${error.message}
                    </div>
                `;
                toast.error('Connection error');
            }
        }
        
        function displayResponse(data) {
            const responseContainer = document.getElementById('response-container');
            
            let html = `
                <div class="vector-store-indicator mb-3">
                    <small class="text-success">
                        <i class="bi bi-database-check"></i> 
                        Retrieved from agricultural knowledge base (${data.sources?.length || 0} source${data.sources?.length !== 1 ? 's' : ''})
                    </small>
                </div>
                
                <div class="message-text mb-3">
                    ${data.response.replace(/\n/g, '<br>')}
                </div>
            `;
            
            if (data.sources && data.sources.length > 0) {
                html += `
                    <div class="message-sources-enhanced">
                        <div class="sources-header">
                            <small class="text-muted">
                                <i class="bi bi-book"></i> Knowledge Sources:
                            </small>
                        </div>
                        <div class="sources-list">
                `;
                
                data.sources.forEach((source, index) => {
                    const relevanceScore = source.relevance_score ? (source.relevance_score * 100).toFixed(1) : 'N/A';
                    html += `
                        <div class="source-item">
                            <div class="source-badge">
                                <span class="badge bg-primary">${index + 1}</span>
                            </div>
                            <div class="source-details">
                                <div class="source-title">${source.title || 'Agricultural Knowledge'}</div>
                                <div class="source-meta">
                                    <small class="text-muted">
                                        Category: ${source.category || 'General'} | 
                                        Relevance: ${relevanceScore}%
                                    </small>
                                </div>
                            </div>
                        </div>
                    `;
                });
                
                html += `
                        </div>
                    </div>
                `;
            }
            
            responseContainer.innerHTML = html;
        }
        
        function updateTestResults(testNum, query, data) {
            const resultsDiv = document.getElementById('test-results');
            const timestamp = new Date().toLocaleTimeString();
            
            const resultHtml = `
                <div class="test-result mb-2 p-2 border rounded">
                    <div class="fw-bold">Test ${testNum} - ${timestamp}</div>
                    <div class="small text-muted">Query: "${query}"</div>
                    <div class="small">
                        <span class="badge bg-success">${data.sources?.length || 0} sources</span>
                        <span class="badge bg-info">${data.status}</span>
                    </div>
                </div>
            `;
            
            if (testNum === 1) {
                resultsDiv.innerHTML = resultHtml;
            } else {
                resultsDiv.insertAdjacentHTML('afterbegin', resultHtml);
            }
        }
        
        // Check health on page load
        document.addEventListener('DOMContentLoaded', function() {
            checkHealth();
        });
    </script>
</body>
</html>
