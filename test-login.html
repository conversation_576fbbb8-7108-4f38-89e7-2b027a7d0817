<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Login</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; }
        .form-group { margin: 15px 0; }
        label { display: block; margin-bottom: 5px; }
        input { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        button { background-color: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background-color: #0056b3; }
        .result { margin-top: 20px; padding: 10px; border-radius: 4px; }
        .success { background-color: #d4edda; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; border: 1px solid #f5c6cb; }
    </style>
</head>
<body>
    <h1>Test Login and Chart-Bot Access</h1>
    
    <div class="form-group">
        <h3>Step 1: Login</h3>
        <form id="loginForm">
            <div class="form-group">
                <label for="email">Email:</label>
                <input type="email" id="email" value="<EMAIL>" required>
            </div>
            <div class="form-group">
                <label for="password">Password:</label>
                <input type="password" id="password" value="password123" required>
            </div>
            <button type="submit">Login</button>
        </form>
    </div>

    <div class="form-group">
        <h3>Step 2: Test Chart-Bot API</h3>
        <button onclick="testChartBotAPI()">Test Chart-Bot API</button>
    </div>

    <div class="form-group">
        <h3>Step 3: Test Manual Question</h3>
        <input type="text" id="testQuestion" placeholder="Enter a question" value="How do I grow tomatoes?">
        <button onclick="testManualQuestion()">Send Question</button>
    </div>

    <div id="results"></div>

    <script>
        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            
            try {
                const response = await fetch('/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `email=${encodeURIComponent(email)}&password=${encodeURIComponent(password)}`,
                    redirect: 'manual' // Don't follow redirects automatically
                });

                if (response.status === 302 || response.status === 200) {
                    showResult('Login successful! You can now test the Chart-Bot API.', 'success');
                } else {
                    showResult('Login failed: ' + response.status, 'error');
                }
            } catch (error) {
                showResult('Login error: ' + error.message, 'error');
            }
        });

        async function testChartBotAPI() {
            try {
                const response = await fetch('/chart-bot/api/health');
                
                if (response.ok) {
                    const data = await response.json();
                    showResult('Chart-Bot API accessible! Response: ' + JSON.stringify(data), 'success');
                } else if (response.status === 401 || response.status === 403) {
                    showResult('Authentication required - please login first', 'error');
                } else {
                    showResult('Chart-Bot API error: ' + response.status, 'error');
                }
            } catch (error) {
                showResult('Chart-Bot API test error: ' + error.message, 'error');
            }
        }

        async function testManualQuestion() {
            const question = document.getElementById('testQuestion').value;
            
            if (!question.trim()) {
                showResult('Please enter a question', 'error');
                return;
            }

            try {
                showResult('Sending question: "' + question + '"...', 'info');
                
                const response = await fetch('/chart-bot/api/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        query: question,
                        maxSources: 3
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    showResult('Question sent successfully! Response: ' + data.response.substring(0, 200) + '...', 'success');
                } else if (response.status === 401 || response.status === 403) {
                    showResult('Authentication required - please login first', 'error');
                } else {
                    const text = await response.text();
                    showResult('Question failed: ' + response.status + ' - ' + text, 'error');
                }
            } catch (error) {
                showResult('Question error: ' + error.message, 'error');
            }
        }

        function showResult(message, type) {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = 'result ' + type;
            div.innerHTML = '<strong>' + new Date().toLocaleTimeString() + ':</strong> ' + message;
            results.appendChild(div);
            results.scrollTop = results.scrollHeight;
        }
    </script>
</body>
</html>
