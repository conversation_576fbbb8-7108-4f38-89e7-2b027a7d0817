import express from 'express';
import { isAuthenticated } from '../middleware/auth.js';
import { 
  saveUser<PERSON><PERSON>, 
  getUser<PERSON><PERSON><PERSON>, 
  saveChatHistory, 
  getChatHistory,
  deleteUser<PERSON>hart 
} from '../services/chartBotService.js';

const router = express.Router();

// Chart-Bot main page
router.get('/', isAuthenticated, async (req, res) => {
  try {
    // Get user's saved charts and recent chat history
    const userCharts = await getUserCharts(req.user.uid);
    const chatHistory = await getChatHistory(req.user.uid, 10); // Get last 10 messages

    res.render('chart-bot/index', {
      title: 'AI Chart Bot - Agricultural Assistant',
      user: req.user,
      userData: req.userData,
      isAuthenticated: true,
      originalUrl: req.originalUrl,
      bodyClass: 'chart-bot-page',
      userCharts: userCharts || [],
      chatHistory: chatHistory || []
    });
  } catch (error) {
    console.error('Error rendering chart-bot page:', error);
    res.render('chart-bot/index', {
      title: 'AI Chart Bot - Agricultural Assistant',
      user: req.user,
      userData: req.userData,
      isAuthenticated: true,
      originalUrl: req.originalUrl,
      bodyClass: 'chart-bot-page',
      userCharts: [],
      chatHistory: [],
      error: 'Failed to load chart-bot page. Please try again later.'
    });
  }
});

// API endpoint to chat with the bot
router.post('/api/chat', isAuthenticated, async (req, res) => {
  try {
    console.log('Chart-bot API chat endpoint called by user:', req.user?.uid);
    const { query, maxSources = 5 } = req.body;

    if (!query || query.trim() === '') {
      console.log('Empty query received');
      return res.status(400).json({
        success: false,
        error: 'Query is required'
      });
    }

    console.log('Sending query to chart-bot service:', query.trim());

    // Call the FastAPI chart-bot service
    const response = await fetch('http://localhost:8000/chat', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query: query.trim(),
        max_sources: maxSources
      })
    });

    console.log('Chart-bot service response status:', response.status);

    if (!response.ok) {
      throw new Error(`Chart-bot service error: ${response.status}`);
    }

    const botResponse = await response.json();
    console.log('Chart-bot service response received successfully');

    // Save chat history to database
    await saveChatHistory(req.user.uid, {
      userMessage: query.trim(),
      botResponse: botResponse.response,
      sources: botResponse.sources,
      timestamp: new Date()
    });

    res.json({
      success: true,
      query: botResponse.query,
      response: botResponse.response,
      sources: botResponse.sources,
      status: botResponse.status
    });

  } catch (error) {
    console.error('Error in chart-bot chat:', error);

    // Check if it's a connection error to the chart-bot service
    const isServiceUnavailable = error.message.includes('fetch') ||
                                 error.message.includes('ECONNREFUSED') ||
                                 error.message.includes('Chart-bot service error');

    if (isServiceUnavailable) {
      // Fallback response if chart-bot service is unavailable
      const fallbackResponse = {
        success: true,
        query: req.body.query,
        response: "I'm sorry, but the AI chart bot service is currently unavailable. Please try again later. In the meantime, you can explore the market trends section for agricultural data visualization.",
        sources: [],
        status: "fallback"
      };

      // Still save the user's message for later processing
      try {
        await saveChatHistory(req.user.uid, {
          userMessage: req.body.query,
          botResponse: fallbackResponse.response,
          sources: [],
          timestamp: new Date(),
          status: 'fallback'
        });
      } catch (saveError) {
        console.error('Error saving fallback chat history:', saveError);
      }

      res.json(fallbackResponse);
    } else {
      // Other errors - return error response
      res.status(500).json({
        success: false,
        error: 'An error occurred while processing your request. Please try again.',
        details: error.message
      });
    }
  }
});

// API endpoint to save a chart
router.post('/api/save-chart', isAuthenticated, async (req, res) => {
  try {
    const { chartData, chartConfig, title, description } = req.body;
    
    if (!chartData || !chartConfig || !title) {
      return res.status(400).json({
        success: false,
        error: 'Chart data, config, and title are required'
      });
    }

    const savedChart = await saveUserChart(req.user.uid, {
      title,
      description: description || '',
      chartData,
      chartConfig,
      createdAt: new Date(),
      updatedAt: new Date()
    });

    res.json({
      success: true,
      message: 'Chart saved successfully',
      chart: savedChart
    });

  } catch (error) {
    console.error('Error saving chart:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to save chart'
    });
  }
});

// API endpoint to get user's saved charts
router.get('/api/charts', isAuthenticated, async (req, res) => {
  try {
    const charts = await getUserCharts(req.user.uid);
    
    res.json({
      success: true,
      charts: charts || []
    });

  } catch (error) {
    console.error('Error fetching user charts:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch charts',
      charts: []
    });
  }
});

// API endpoint to delete a chart
router.delete('/api/charts/:chartId', isAuthenticated, async (req, res) => {
  try {
    const { chartId } = req.params;
    
    await deleteUserChart(req.user.uid, chartId);
    
    res.json({
      success: true,
      message: 'Chart deleted successfully'
    });

  } catch (error) {
    console.error('Error deleting chart:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to delete chart'
    });
  }
});

// API endpoint to get chat history
router.get('/api/chat-history', isAuthenticated, async (req, res) => {
  try {
    const { limit = 20 } = req.query;
    const history = await getChatHistory(req.user.uid, parseInt(limit));
    
    res.json({
      success: true,
      history: history || []
    });

  } catch (error) {
    console.error('Error fetching chat history:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch chat history',
      history: []
    });
  }
});

// API endpoint to check chart-bot service health (no auth required for testing)
router.get('/api/health', async (req, res) => {
  try {
    const response = await fetch('http://localhost:8000/health');
    const healthData = await response.json();

    res.json({
      success: true,
      chartBotStatus: response.ok ? 'healthy' : 'unhealthy',
      details: healthData
    });

  } catch (error) {
    console.error('Chart-bot health check failed:', error);
    res.json({
      success: false,
      chartBotStatus: 'unavailable',
      error: 'Chart-bot service is not available'
    });
  }
});

// Test endpoint for chart-bot service (no auth required for testing)
router.post('/api/test-chat', async (req, res) => {
  try {
    console.log('Test chart-bot API endpoint called');
    const { query, maxSources = 5 } = req.body;

    if (!query || query.trim() === '') {
      console.log('Empty query received');
      return res.status(400).json({
        success: false,
        error: 'Query is required'
      });
    }

    console.log('Sending test query to chart-bot service:', query.trim());

    // Call the FastAPI chart-bot service
    const response = await fetch('http://localhost:8000/chat', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query: query.trim(),
        max_sources: maxSources
      })
    });

    console.log('Chart-bot service response status:', response.status);

    if (!response.ok) {
      throw new Error(`Chart-bot service error: ${response.status}`);
    }

    const botResponse = await response.json();
    console.log('Chart-bot service response received successfully');

    res.json({
      success: true,
      query: botResponse.query,
      response: botResponse.response,
      sources: botResponse.sources,
      status: botResponse.status
    });

  } catch (error) {
    console.error('Error in test chart-bot chat:', error);
    res.status(500).json({
      success: false,
      error: 'An error occurred while processing your request. Please try again.',
      details: error.message
    });
  }
});

export default router;
