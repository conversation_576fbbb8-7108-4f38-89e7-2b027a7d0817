<%- include('../partials/header') %>

<!-- Chart GPT Style Layout -->
<div class="chartgpt-container">
  <!-- Header -->
  <header class="chartgpt-header">
    <div class="header-content">
      <div class="nav-links">
        <a href="/dashboard" class="nav-link">
          <i class="bi bi-house-door"></i>
          <span>Home</span>
        </a>
        <a href="/chart-bot" class="nav-link active">
          <i class="bi bi-robot"></i>
          <span>Chart Bot</span>
        </a>
        <a href="/market-trends" class="nav-link">
          <i class="bi bi-graph-up"></i>
          <span>Market</span>
        </a>
      </div>

      <div class="header-actions">
        <button class="btn-minimal" id="clear-history-btn" title="Clear History">
          <i class="bi bi-trash"></i>
        </button>
        <button class="btn-minimal" id="show-charts-btn" title="My Charts">
          <i class="bi bi-collection"></i>
        </button>
      </div>
    </div>
  </header>

  <!-- Main Content -->
  <main class="chartgpt-main">
    <!-- Title Section -->
    <div class="chartgpt-title">
      <h1>ChartGPT</h1>
      <p>Your intelligent agricultural assistant for data visualization and insights</p>
    </div>

    <!-- Status Indicator -->
    <div class="status-indicator" id="bot-status">
      <span class="status-badge checking">Checking...</span>
    </div>

    <!-- Error Display -->
    <% if (typeof error !== 'undefined' && error) { %>
      <div class="error-message">
        <i class="bi bi-exclamation-triangle"></i>
        <%= error %>
      </div>
    <% } %>

    <!-- Chart Display Area -->
    <div class="chart-display-area" id="chart-display">
      <!-- Chart will be displayed here -->
    </div>

    <!-- Chat Messages Container -->
    <div class="chat-container">
      <div id="chat-messages" class="chat-messages">
        <!-- Welcome Message -->
        <div class="message bot-message">
          <div class="message-content">
            Hello! I'm your AI agricultural assistant. I can help you with crop diseases, pest management, soil health, irrigation, and data visualization. Ask me anything about farming!
          </div>
        </div>

        <!-- Load Previous Chat History -->
        <% if (chatHistory && chatHistory.length > 0) { %>
          <% chatHistory.forEach(chat => { %>
            <div class="message user-message">
              <div class="message-content"><%= chat.userMessage %></div>
            </div>

            <div class="message bot-message">
              <div class="message-content">
                <%= chat.botResponse %>
                <% if (chat.sources && chat.sources.length > 0) { %>
                  <div class="message-sources">
                    <% chat.sources.forEach(source => { %>
                      <span class="source-tag"><%= source.title || 'Agricultural Knowledge' %></span>
                    <% }); %>
                  </div>
                <% } %>
              </div>
            </div>
          <% }); %>
        <% } %>
      </div>
    </div>

    <!-- Input Area -->
    <div class="input-area">
      <div class="input-container">
        <input type="text"
               id="chat-input"
               class="chat-input"
               placeholder="Anything you want to change?">
        <button class="send-button" id="send-button">
          <i class="bi bi-arrow-up"></i>
        </button>
      </div>

      <div class="suggestions">
        <button class="suggestion-btn" data-suggestion="How do I treat tomato blight?">
          Tomato blight treatment
        </button>
        <button class="suggestion-btn" data-suggestion="Show me corn price trends">
          Corn price trends
        </button>
        <button class="suggestion-btn" data-suggestion="Best fertilizers for vegetables">
          Fertilizer recommendations
        </button>
        <button class="suggestion-btn lucky-btn" onclick="generateSampleChart()">
          I'm feeling lucky!
        </button>
      </div>
    </div>
  </main>

  <!-- Saved Charts Sidebar (Hidden by default, shown on demand) -->
  <div class="charts-sidebar" id="charts-sidebar">
    <div class="sidebar-header">
      <h3>My Saved Charts</h3>
      <button class="close-sidebar" id="close-charts-sidebar">
        <i class="bi bi-x"></i>
      </button>
    </div>
    <div class="sidebar-content">
      <div id="saved-charts-list">
        <% if (userCharts && userCharts.length > 0) { %>
          <% userCharts.forEach(chart => { %>
            <div class="saved-chart-item" data-chart-id="<%= chart.id %>">
              <div class="chart-info">
                <h4><%= chart.title %></h4>
                <small><%= new Date(chart.createdAt?.toDate ? chart.createdAt.toDate() : chart.createdAt).toLocaleDateString() %></small>
              </div>
              <div class="chart-actions">
                <button class="btn-icon load-chart-btn" data-chart-id="<%= chart.id %>" title="View Chart">
                  <i class="bi bi-eye"></i>
                </button>
                <button class="btn-icon delete-chart-btn" data-chart-id="<%= chart.id %>" title="Delete Chart">
                  <i class="bi bi-trash"></i>
                </button>
              </div>
            </div>
          <% }); %>
        <% } else { %>
          <p class="no-charts">No saved charts yet. Start chatting to create visualizations!</p>
        <% } %>
      </div>
    </div>
  </div>
</div>

<!-- Chart Modal -->
<div class="chart-modal" id="chartModal">
  <div class="modal-overlay" onclick="closeChartModal()"></div>
  <div class="modal-content">
    <div class="modal-header">
      <h3>Chart Visualization</h3>
      <button class="close-modal" onclick="closeChartModal()">
        <i class="bi bi-x"></i>
      </button>
    </div>
    <div class="modal-body">
      <div id="chart-container">
        <!-- Chart will be rendered here -->
      </div>
    </div>
    <div class="modal-footer">
      <button class="btn-secondary" onclick="closeChartModal()">Close</button>
      <button class="btn-primary" id="save-chart-btn">
        <i class="bi bi-save"></i> Save Chart
      </button>
    </div>
  </div>
</div>

<%- include('../partials/footer') %>

<!-- Chart Bot Scripts -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="/js/toast-notifications.js"></script>
<script src="/js/chart-bot.js"></script>

<script>
  // Chart GPT Style Functions
  function closeChartModal() {
    const modal = document.getElementById('chartModal');
    if (modal) {
      modal.style.display = 'none';
    }
  }

  function showChartModal() {
    const modal = document.getElementById('chartModal');
    if (modal) {
      modal.style.display = 'flex';
    }
  }

  // Initialize chart bot when page loads
  document.addEventListener('DOMContentLoaded', function() {
    console.log('Chart Bot page DOM loaded');

    // Set up Chart GPT style functionality
    setupChartGPTFunctionality();

    // Initialize original chart bot functionality
    setTimeout(function() {
      if (typeof initializeChartBot === 'function') {
        console.log('Initializing Chart Bot...');
        initializeChartBot();
      } else {
        console.error('initializeChartBot function not available');
        setupBasicFunctionality();
      }
    }, 500);
  });

  function setupChartGPTFunctionality() {
    // Charts sidebar toggle
    const showChartsBtn = document.getElementById('show-charts-btn');
    const chartsSidebar = document.getElementById('charts-sidebar');
    const closeChartsBtn = document.getElementById('close-charts-sidebar');

    if (showChartsBtn && chartsSidebar) {
      showChartsBtn.addEventListener('click', function() {
        chartsSidebar.classList.add('active');
      });
    }

    if (closeChartsBtn && chartsSidebar) {
      closeChartsBtn.addEventListener('click', function() {
        chartsSidebar.classList.remove('active');
      });
    }

    // Suggestion buttons
    const suggestionBtns = document.querySelectorAll('.suggestion-btn');
    suggestionBtns.forEach(btn => {
      if (!btn.classList.contains('lucky-btn')) {
        btn.addEventListener('click', function() {
          const suggestion = this.getAttribute('data-suggestion');
          if (suggestion) {
            const chatInput = document.getElementById('chat-input');
            if (chatInput) {
              chatInput.value = suggestion;
              if (typeof window.sendMessage === 'function') {
                window.sendMessage();
              }
            }
          }
        });
      }
    });
  }

  function setupBasicFunctionality() {
    console.log('Setting up basic chart bot functionality...');

    const sendButton = document.getElementById('send-button');
    const chatInput = document.getElementById('chat-input');

    if (sendButton && chatInput) {
      sendButton.addEventListener('click', function() {
        if (typeof window.sendMessage === 'function') {
          window.sendMessage();
        } else {
          console.error('sendMessage function not available');
        }
      });

      chatInput.addEventListener('keypress', function(event) {
        if (event.key === 'Enter' && !event.shiftKey) {
          event.preventDefault();
          if (typeof window.sendMessage === 'function') {
            window.sendMessage();
          } else {
            console.error('sendMessage function not available');
          }
        }
      });

      console.log('Basic event listeners set up successfully');
    } else {
      console.error('Chat input or send button not found');
    }
  }
</script>
