<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chart Bot Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 3px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        #results {
            margin-top: 20px;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <h1>Chart Bot Functionality Test</h1>
    
    <div class="test-section">
        <h3>Test 1: Check if server is running</h3>
        <button onclick="testServerHealth()">Test Server Health</button>
        <div id="server-result"></div>
    </div>

    <div class="test-section">
        <h3>Test 2: Check chart-bot service</h3>
        <button onclick="testChartBotService()">Test Chart Bot Service</button>
        <div id="chartbot-result"></div>
    </div>

    <div class="test-section">
        <h3>Test 3: Test API endpoint (requires login)</h3>
        <button onclick="testAPIEndpoint()">Test API Endpoint</button>
        <div id="api-result"></div>
    </div>

    <div class="test-section">
        <h3>Test 4: Check DOM elements</h3>
        <button onclick="testDOMElements()">Test DOM Elements</button>
        <div id="dom-result"></div>
    </div>

    <div id="results"></div>

    <script>
        async function testServerHealth() {
            const resultDiv = document.getElementById('server-result');
            try {
                const response = await fetch('http://localhost:3002/');
                if (response.ok) {
                    resultDiv.innerHTML = '<div class="success">✅ Server is running on port 3002</div>';
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Server responded with status: ' + response.status + '</div>';
                }
            } catch (error) {
                resultDiv.innerHTML = '<div class="error">❌ Cannot connect to server: ' + error.message + '</div>';
            }
        }

        async function testChartBotService() {
            const resultDiv = document.getElementById('chartbot-result');
            try {
                const response = await fetch('http://localhost:8000/health');
                if (response.ok) {
                    const data = await response.json();
                    resultDiv.innerHTML = '<div class="success">✅ Chart Bot service is healthy: ' + JSON.stringify(data) + '</div>';
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Chart Bot service error: ' + response.status + '</div>';
                }
            } catch (error) {
                resultDiv.innerHTML = '<div class="error">❌ Cannot connect to Chart Bot service: ' + error.message + '</div>';
            }
        }

        async function testAPIEndpoint() {
            const resultDiv = document.getElementById('api-result');
            try {
                const response = await fetch('http://localhost:3002/chart-bot/api/health');
                if (response.ok) {
                    const data = await response.json();
                    resultDiv.innerHTML = '<div class="success">✅ API endpoint accessible: ' + JSON.stringify(data) + '</div>';
                } else if (response.status === 401 || response.status === 403) {
                    resultDiv.innerHTML = '<div class="warning">⚠️ Authentication required (this is expected)</div>';
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ API endpoint error: ' + response.status + '</div>';
                }
            } catch (error) {
                resultDiv.innerHTML = '<div class="error">❌ Cannot connect to API endpoint: ' + error.message + '</div>';
            }
        }

        function testDOMElements() {
            const resultDiv = document.getElementById('dom-result');
            const results = [];
            
            // Test if we can access the chart-bot page elements
            try {
                // This test only works if we're on the actual chart-bot page
                const chatInput = document.getElementById('chat-input');
                const sendButton = document.getElementById('send-button');
                
                if (chatInput) {
                    results.push('✅ Chat input element found');
                } else {
                    results.push('❌ Chat input element not found (normal if not on chart-bot page)');
                }
                
                if (sendButton) {
                    results.push('✅ Send button element found');
                } else {
                    results.push('❌ Send button element not found (normal if not on chart-bot page)');
                }
                
                resultDiv.innerHTML = '<div class="warning">' + results.join('<br>') + '</div>';
            } catch (error) {
                resultDiv.innerHTML = '<div class="error">❌ DOM test error: ' + error.message + '</div>';
            }
        }

        // Run initial tests
        window.onload = function() {
            testServerHealth();
            testChartBotService();
        };
    </script>
</body>
</html>
