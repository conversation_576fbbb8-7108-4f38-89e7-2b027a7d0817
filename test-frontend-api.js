// Test script to verify chart-bot frontend API functionality
console.log('Testing Chart Bot Frontend API...');

// Test 1: Check if we can reach the Node.js server
async function testNodeServer() {
    try {
        const response = await fetch('http://localhost:3002/');
        console.log('✅ Node.js server is reachable, status:', response.status);
        return true;
    } catch (error) {
        console.error('❌ Cannot reach Node.js server:', error.message);
        return false;
    }
}

// Test 2: Test chart-bot health endpoint (requires authentication)
async function testChartBotHealth() {
    try {
        const response = await fetch('http://localhost:3002/chart-bot/api/health');
        console.log('Chart-bot health endpoint status:', response.status);
        
        if (response.status === 401 || response.status === 403) {
            console.log('⚠️ Authentication required (expected for health endpoint)');
            return true;
        } else if (response.ok) {
            const data = await response.json();
            console.log('✅ Chart-bot health endpoint response:', data);
            return true;
        } else {
            console.log('❌ Unexpected status from health endpoint:', response.status);
            return false;
        }
    } catch (error) {
        console.error('❌ Error testing health endpoint:', error.message);
        return false;
    }
}

// Test 3: Test chart-bot chat endpoint (requires authentication)
async function testChartBotChat() {
    try {
        const response = await fetch('http://localhost:3002/chart-bot/api/chat', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                query: 'How do I grow tomatoes?',
                maxSources: 3
            })
        });
        
        console.log('Chart-bot chat endpoint status:', response.status);
        
        if (response.status === 401 || response.status === 403) {
            console.log('⚠️ Authentication required for chat endpoint (expected)');
            return true;
        } else if (response.ok) {
            const data = await response.json();
            console.log('✅ Chart-bot chat endpoint response:', data);
            return true;
        } else {
            console.log('❌ Unexpected status from chat endpoint:', response.status);
            const text = await response.text();
            console.log('Response text:', text);
            return false;
        }
    } catch (error) {
        console.error('❌ Error testing chat endpoint:', error.message);
        return false;
    }
}

// Test 4: Test direct chart-bot service
async function testDirectChartBot() {
    try {
        const response = await fetch('http://localhost:8000/chat', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                query: 'How do I grow tomatoes?',
                max_sources: 3
            })
        });
        
        if (response.ok) {
            const data = await response.json();
            console.log('✅ Direct chart-bot service is working');
            console.log('Response preview:', data.response.substring(0, 100) + '...');
            return true;
        } else {
            console.log('❌ Direct chart-bot service error:', response.status);
            return false;
        }
    } catch (error) {
        console.error('❌ Error testing direct chart-bot service:', error.message);
        return false;
    }
}

// Run all tests
async function runAllTests() {
    console.log('\n=== Chart Bot API Tests ===\n');
    
    const results = {
        nodeServer: await testNodeServer(),
        chartBotHealth: await testChartBotHealth(),
        chartBotChat: await testChartBotChat(),
        directChartBot: await testDirectChartBot()
    };
    
    console.log('\n=== Test Results ===');
    console.log('Node.js Server:', results.nodeServer ? '✅ PASS' : '❌ FAIL');
    console.log('Chart-bot Health:', results.chartBotHealth ? '✅ PASS' : '❌ FAIL');
    console.log('Chart-bot Chat:', results.chartBotChat ? '✅ PASS' : '❌ FAIL');
    console.log('Direct Chart-bot:', results.directChartBot ? '✅ PASS' : '❌ FAIL');
    
    const allPassed = Object.values(results).every(result => result);
    console.log('\nOverall Status:', allPassed ? '✅ ALL TESTS PASSED' : '⚠️ SOME TESTS FAILED');
    
    if (allPassed) {
        console.log('\n🎉 The chart-bot backend services are working correctly!');
        console.log('If questions are not being sent, the issue is likely:');
        console.log('1. User authentication (need to be logged in)');
        console.log('2. Frontend JavaScript event listeners not working');
        console.log('3. Browser console errors');
    }
}

// Run tests if in Node.js environment
if (typeof module !== 'undefined' && module.exports) {
    runAllTests();
}

// Export for browser use
if (typeof window !== 'undefined') {
    window.runChartBotTests = runAllTests;
}
