<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Chart Bot Frontend</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 3px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            background-color: #007bff;
            color: white;
        }
        button:hover {
            background-color: #0056b3;
        }
        #chat-input {
            width: 70%;
            padding: 10px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 3px;
        }
        #send-button {
            padding: 10px 20px;
            background-color: #28a745;
        }
        #send-button:hover {
            background-color: #218838;
        }
        #results {
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <h1>Chart Bot Frontend Test</h1>
    
    <div class="test-section">
        <h3>1. Test Backend Services</h3>
        <button onclick="testNodeJSServer()">Test Node.js Server</button>
        <button onclick="testChartBotService()">Test Chart Bot Service</button>
        <button onclick="testChartBotAPI()">Test Chart Bot API</button>
    </div>

    <div class="test-section">
        <h3>2. Test Frontend Chat</h3>
        <input type="text" id="chat-input" placeholder="Type your message here..." value="How do I grow tomatoes?">
        <button id="send-button" onclick="testSendMessage()">Send Message</button>
    </div>

    <div class="test-section">
        <h3>3. Test Authentication</h3>
        <button onclick="testAuthentication()">Check Authentication Status</button>
    </div>

    <div id="results"></div>

    <script>
        function addResult(message, type = 'info') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `result ${type}`;
            div.innerHTML = `<strong>${new Date().toLocaleTimeString()}</strong>: ${message}`;
            results.appendChild(div);
            results.scrollTop = results.scrollHeight;
        }

        async function testNodeJSServer() {
            try {
                const response = await fetch('/');
                if (response.ok) {
                    addResult('✅ Node.js server is running', 'success');
                } else {
                    addResult(`❌ Node.js server error: ${response.status}`, 'error');
                }
            } catch (error) {
                addResult(`❌ Node.js server connection failed: ${error.message}`, 'error');
            }
        }

        async function testChartBotService() {
            try {
                const response = await fetch('http://localhost:8000/health');
                if (response.ok) {
                    const data = await response.json();
                    addResult(`✅ Chart Bot service is ${data.status}: ${data.message}`, 'success');
                } else {
                    addResult(`❌ Chart Bot service error: ${response.status}`, 'error');
                }
            } catch (error) {
                addResult(`❌ Chart Bot service connection failed: ${error.message}`, 'error');
            }
        }

        async function testChartBotAPI() {
            try {
                const response = await fetch('/chart-bot/api/health');
                if (response.ok) {
                    const data = await response.json();
                    addResult(`✅ Chart Bot API is working: ${data.message || 'OK'}`, 'success');
                } else {
                    addResult(`❌ Chart Bot API error: ${response.status}`, 'error');
                }
            } catch (error) {
                addResult(`❌ Chart Bot API connection failed: ${error.message}`, 'error');
            }
        }

        async function testSendMessage() {
            const chatInput = document.getElementById('chat-input');
            const sendButton = document.getElementById('send-button');
            const message = chatInput.value.trim();

            if (!message) {
                addResult('❌ Please enter a message', 'error');
                return;
            }

            addResult(`📤 Sending message: "${message}"`, 'info');

            // Disable button
            sendButton.disabled = true;
            sendButton.textContent = 'Sending...';

            try {
                // Use test endpoint that doesn't require authentication
                const response = await fetch('/chart-bot/api/test-chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        query: message,
                        maxSources: 5
                    })
                });

                addResult(`📡 Response status: ${response.status}`, 'info');

                if (!response.ok) {
                    const errorText = await response.text();
                    addResult(`❌ HTTP error ${response.status}: ${errorText}`, 'error');
                    return;
                }

                const data = await response.json();
                addResult(`✅ Response received successfully`, 'success');
                addResult(`🤖 Bot response: ${data.response.substring(0, 200)}...`, 'info');

                if (data.sources && data.sources.length > 0) {
                    addResult(`📚 Sources found: ${data.sources.length}`, 'info');
                    data.sources.forEach((source, index) => {
                        addResult(`   ${index + 1}. ${source.title || 'Agricultural Knowledge'} (${source.category || 'General'})`, 'info');
                    });
                }

            } catch (error) {
                addResult(`❌ Error sending message: ${error.message}`, 'error');
            } finally {
                // Re-enable button
                sendButton.disabled = false;
                sendButton.textContent = 'Send Message';
            }
        }

        async function testAuthentication() {
            try {
                const response = await fetch('/chart-bot/');
                if (response.ok) {
                    addResult('✅ Authentication check passed - can access chart bot page', 'success');
                } else if (response.status === 401 || response.status === 403) {
                    addResult('❌ Authentication required - please log in', 'error');
                } else {
                    addResult(`❌ Authentication check failed: ${response.status}`, 'error');
                }
            } catch (error) {
                addResult(`❌ Authentication check error: ${error.message}`, 'error');
            }
        }

        // Auto-run basic tests on page load
        window.addEventListener('load', function() {
            addResult('🚀 Starting frontend tests...', 'info');
            setTimeout(() => {
                testNodeJSServer();
                setTimeout(() => testChartBotService(), 500);
                setTimeout(() => testChartBotAPI(), 1000);
                setTimeout(() => testAuthentication(), 1500);
            }, 500);
        });

        // Allow Enter key to send message
        document.getElementById('chat-input').addEventListener('keypress', function(event) {
            if (event.key === 'Enter') {
                testSendMessage();
            }
        });
    </script>
</body>
</html>
