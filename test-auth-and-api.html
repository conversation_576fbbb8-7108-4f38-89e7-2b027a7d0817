<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Authentication and API Test</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; }
        button { background-color: #007bff; color: white; border: none; padding: 10px 15px; border-radius: 3px; cursor: pointer; margin: 5px; }
        button:hover { background-color: #0056b3; }
        pre { background-color: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>Authentication and API Test</h1>
    
    <div class="test-section">
        <h3>Test 1: Check Authentication Status</h3>
        <button onclick="testAuth()">Test Authentication</button>
        <div id="auth-result"></div>
    </div>

    <div class="test-section">
        <h3>Test 2: Test Chart-Bot API (with auth)</h3>
        <button onclick="testChartBotAPI()">Test Chart-Bot API</button>
        <div id="api-result"></div>
    </div>

    <div class="test-section">
        <h3>Test 3: Test Direct Chart-Bot Service</h3>
        <button onclick="testDirectService()">Test Direct Service</button>
        <div id="direct-result"></div>
    </div>

    <div class="test-section">
        <h3>Test 4: Manual API Call</h3>
        <input type="text" id="test-query" placeholder="Enter test question" value="How do I grow tomatoes?">
        <button onclick="manualAPICall()">Send Manual API Call</button>
        <div id="manual-result"></div>
    </div>

    <script>
        async function testAuth() {
            const resultDiv = document.getElementById('auth-result');
            try {
                // Try to access a protected endpoint
                const response = await fetch('/chart-bot/api/health');
                
                if (response.status === 401 || response.status === 403) {
                    resultDiv.innerHTML = '<div class="error">❌ Not authenticated - need to log in</div>';
                } else if (response.ok) {
                    const data = await response.json();
                    resultDiv.innerHTML = '<div class="success">✅ Authenticated successfully<pre>' + JSON.stringify(data, null, 2) + '</pre></div>';
                } else {
                    resultDiv.innerHTML = '<div class="warning">⚠️ Unexpected response: ' + response.status + '</div>';
                }
            } catch (error) {
                resultDiv.innerHTML = '<div class="error">❌ Error: ' + error.message + '</div>';
            }
        }

        async function testChartBotAPI() {
            const resultDiv = document.getElementById('api-result');
            try {
                const response = await fetch('/chart-bot/api/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        query: 'Test question',
                        maxSources: 3
                    })
                });

                if (response.status === 401 || response.status === 403) {
                    resultDiv.innerHTML = '<div class="error">❌ Authentication required</div>';
                } else if (response.ok) {
                    const data = await response.json();
                    resultDiv.innerHTML = '<div class="success">✅ API working<pre>' + JSON.stringify(data, null, 2) + '</pre></div>';
                } else {
                    const text = await response.text();
                    resultDiv.innerHTML = '<div class="error">❌ API error: ' + response.status + '<pre>' + text + '</pre></div>';
                }
            } catch (error) {
                resultDiv.innerHTML = '<div class="error">❌ Error: ' + error.message + '</div>';
            }
        }

        async function testDirectService() {
            const resultDiv = document.getElementById('direct-result');
            try {
                const response = await fetch('http://localhost:8000/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        query: 'Test question',
                        max_sources: 3
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    resultDiv.innerHTML = '<div class="success">✅ Direct service working<pre>' + JSON.stringify(data, null, 2) + '</pre></div>';
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Direct service error: ' + response.status + '</div>';
                }
            } catch (error) {
                resultDiv.innerHTML = '<div class="error">❌ Error: ' + error.message + '</div>';
            }
        }

        async function manualAPICall() {
            const resultDiv = document.getElementById('manual-result');
            const query = document.getElementById('test-query').value;
            
            if (!query.trim()) {
                resultDiv.innerHTML = '<div class="warning">⚠️ Please enter a question</div>';
                return;
            }

            try {
                resultDiv.innerHTML = '<div class="warning">⏳ Sending request...</div>';
                
                const response = await fetch('/chart-bot/api/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        query: query,
                        maxSources: 5
                    })
                });

                console.log('Response status:', response.status);
                console.log('Response headers:', response.headers);

                if (response.status === 401 || response.status === 403) {
                    resultDiv.innerHTML = '<div class="error">❌ Authentication required - please log in first</div>';
                } else if (response.ok) {
                    const data = await response.json();
                    console.log('Response data:', data);
                    resultDiv.innerHTML = '<div class="success">✅ Success!<pre>' + JSON.stringify(data, null, 2) + '</pre></div>';
                } else {
                    const text = await response.text();
                    console.log('Error response:', text);
                    resultDiv.innerHTML = '<div class="error">❌ Error: ' + response.status + '<pre>' + text + '</pre></div>';
                }
            } catch (error) {
                console.error('Request error:', error);
                resultDiv.innerHTML = '<div class="error">❌ Request failed: ' + error.message + '</div>';
            }
        }

        // Auto-run authentication test
        window.onload = function() {
            testAuth();
        };
    </script>
</body>
</html>
