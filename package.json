{"name": "sustainable_farming", "version": "1.0.0", "main": "src/index.js", "type": "module", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "test": "echo \"Error: no test specified\" && exit 1", "emulators": "firebase emulators:start", "dev:emulators": "concurrently \"npm run emulators\" \"npm run dev\""}, "keywords": ["sustainable", "farming", "agriculture", "eco-friendly"], "author": "", "license": "ISC", "description": "A web application for sustainable farming practices", "dependencies": {"axios": "^1.9.0", "cookie-parser": "^1.4.7", "dotenv": "^16.5.0", "ejs": "^3.1.10", "exceljs": "^4.4.0", "express": "^5.1.0", "express-ejs-layouts": "^2.5.1", "express-session": "^1.18.1", "firebase": "^11.6.1", "firebase-admin": "^13.4.0", "json2csv": "^6.0.0-alpha.2", "multer": "^1.4.5-lts.2", "node-fetch": "^2.7.0"}, "devDependencies": {"concurrently": "^8.2.2", "firebase-tools": "^13.5.0", "nodemon": "^3.1.10"}}