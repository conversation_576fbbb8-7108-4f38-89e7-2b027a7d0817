import {
  collection,
  doc,
  setDoc,
  getDoc,
  getDocs,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  limit,
  serverTimestamp,
  addDoc
} from 'firebase/firestore';
import { db } from '../config/initFirebase.js';

// Collection names
const CHART_BOT_CHATS_COLLECTION = 'chartBotChats';
const USER_CHARTS_COLLECTION = 'userCharts';

/**
 * Save chat history to Firestore
 */
export const saveChatHistory = async (userId, chatData) => {
  try {
    const chatRef = await addDoc(collection(db, CHART_BOT_CHATS_COLLECTION), {
      userId,
      userMessage: chatData.userMessage,
      botResponse: chatData.botResponse,
      sources: chatData.sources || [],
      status: chatData.status || 'success',
      createdAt: serverTimestamp(),
      timestamp: chatData.timestamp || new Date()
    });

    console.log('Chat history saved with ID:', chatRef.id);
    return chatRef.id;
  } catch (error) {
    console.error('Error saving chat history:', error);
    throw error;
  }
};

/**
 * Get chat history for a user
 */
export const getChatHistory = async (userId, limitCount = 20) => {
  try {
    const q = query(
      collection(db, CHART_BOT_CHATS_COLLECTION),
      where('userId', '==', userId),
      orderBy('createdAt', 'desc'),
      limit(limitCount)
    );

    const querySnapshot = await getDocs(q);
    const chatHistory = [];

    querySnapshot.forEach((doc) => {
      chatHistory.push({
        id: doc.id,
        ...doc.data()
      });
    });

    // Reverse to get chronological order (oldest first)
    return chatHistory.reverse();
  } catch (error) {
    console.error('Error fetching chat history:', error);

    // If it's an index error, provide helpful information
    if (error.code === 'failed-precondition' && error.message.includes('index')) {
      console.log('Firebase index required for chartBotChats collection');
      console.log('Create index: userId (Ascending) + createdAt (Descending)');
    }

    return [];
  }
};

/**
 * Save a user's chart to Firestore
 */
export const saveUserChart = async (userId, chartData) => {
  try {
    const chartRef = await addDoc(collection(db, USER_CHARTS_COLLECTION), {
      userId,
      title: chartData.title,
      description: chartData.description || '',
      chartData: chartData.chartData,
      chartConfig: chartData.chartConfig,
      chartType: chartData.chartType || 'line',
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    });

    console.log('Chart saved with ID:', chartRef.id);
    
    // Return the saved chart with ID
    const savedChart = {
      id: chartRef.id,
      userId,
      title: chartData.title,
      description: chartData.description || '',
      chartData: chartData.chartData,
      chartConfig: chartData.chartConfig,
      chartType: chartData.chartType || 'line',
      createdAt: new Date(),
      updatedAt: new Date()
    };

    return savedChart;
  } catch (error) {
    console.error('Error saving chart:', error);
    throw error;
  }
};

/**
 * Get all charts for a user
 */
export const getUserCharts = async (userId) => {
  try {
    const q = query(
      collection(db, USER_CHARTS_COLLECTION),
      where('userId', '==', userId),
      orderBy('createdAt', 'desc')
    );

    const querySnapshot = await getDocs(q);
    const charts = [];

    querySnapshot.forEach((doc) => {
      charts.push({
        id: doc.id,
        ...doc.data()
      });
    });

    return charts;
  } catch (error) {
    console.error('Error fetching user charts:', error);

    // If it's an index error, provide helpful information
    if (error.code === 'failed-precondition' && error.message.includes('index')) {
      console.log('Firebase index required for userCharts collection');
      console.log('Create index: userId (Ascending) + createdAt (Descending)');
    }

    return [];
  }
};

/**
 * Update a user's chart
 */
export const updateUserChart = async (userId, chartId, updateData) => {
  try {
    const chartRef = doc(db, USER_CHARTS_COLLECTION, chartId);
    
    // First check if the chart belongs to the user
    const chartDoc = await getDoc(chartRef);
    if (!chartDoc.exists()) {
      throw new Error('Chart not found');
    }

    const chartData = chartDoc.data();
    if (chartData.userId !== userId) {
      throw new Error('Unauthorized: Chart does not belong to user');
    }

    // Update the chart
    await updateDoc(chartRef, {
      ...updateData,
      updatedAt: serverTimestamp()
    });

    console.log('Chart updated:', chartId);
    return true;
  } catch (error) {
    console.error('Error updating chart:', error);
    throw error;
  }
};

/**
 * Delete a user's chart
 */
export const deleteUserChart = async (userId, chartId) => {
  try {
    const chartRef = doc(db, USER_CHARTS_COLLECTION, chartId);
    
    // First check if the chart belongs to the user
    const chartDoc = await getDoc(chartRef);
    if (!chartDoc.exists()) {
      throw new Error('Chart not found');
    }

    const chartData = chartDoc.data();
    if (chartData.userId !== userId) {
      throw new Error('Unauthorized: Chart does not belong to user');
    }

    // Delete the chart
    await deleteDoc(chartRef);

    console.log('Chart deleted:', chartId);
    return true;
  } catch (error) {
    console.error('Error deleting chart:', error);
    throw error;
  }
};

/**
 * Get a specific chart by ID
 */
export const getChartById = async (userId, chartId) => {
  try {
    const chartRef = doc(db, USER_CHARTS_COLLECTION, chartId);
    const chartDoc = await getDoc(chartRef);

    if (!chartDoc.exists()) {
      return null;
    }

    const chartData = chartDoc.data();
    
    // Check if the chart belongs to the user
    if (chartData.userId !== userId) {
      throw new Error('Unauthorized: Chart does not belong to user');
    }

    return {
      id: chartDoc.id,
      ...chartData
    };
  } catch (error) {
    console.error('Error fetching chart by ID:', error);
    throw error;
  }
};

/**
 * Clear chat history for a user (optional utility function)
 */
export const clearChatHistory = async (userId) => {
  try {
    const q = query(
      collection(db, CHART_BOT_CHATS_COLLECTION),
      where('userId', '==', userId)
    );

    const querySnapshot = await getDocs(q);
    const deletePromises = [];

    querySnapshot.forEach((doc) => {
      deletePromises.push(deleteDoc(doc.ref));
    });

    await Promise.all(deletePromises);
    console.log('Chat history cleared for user:', userId);
    return true;
  } catch (error) {
    console.error('Error clearing chat history:', error);
    throw error;
  }
};

/**
 * Get chat statistics for a user
 */
export const getChatStatistics = async (userId) => {
  try {
    const q = query(
      collection(db, CHART_BOT_CHATS_COLLECTION),
      where('userId', '==', userId)
    );

    const querySnapshot = await getDocs(q);
    let totalChats = 0;
    let successfulChats = 0;
    let fallbackChats = 0;

    querySnapshot.forEach((doc) => {
      const data = doc.data();
      totalChats++;
      
      if (data.status === 'success') {
        successfulChats++;
      } else if (data.status === 'fallback') {
        fallbackChats++;
      }
    });

    return {
      totalChats,
      successfulChats,
      fallbackChats,
      successRate: totalChats > 0 ? (successfulChats / totalChats) * 100 : 0
    };
  } catch (error) {
    console.error('Error fetching chat statistics:', error);
    return {
      totalChats: 0,
      successfulChats: 0,
      fallbackChats: 0,
      successRate: 0
    };
  }
};
