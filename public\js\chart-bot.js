/**
 * Chart Bot JavaScript
 * Handles chat interface, chart generation, and data visualization
 */

let currentChart = null;
let chartBotStatus = 'unknown';

// Immediately define global functions to prevent "not defined" errors
window.sendMessage = function() { console.log('sendMessage called before initialization'); };
window.sendSuggestion = function() { console.log('sendSuggestion called before initialization'); };
window.handleChatKeyPress = function() { console.log('handleChatKeyPress called before initialization'); };
window.showSavedCharts = function() { console.log('showSavedCharts called before initialization'); };
window.initializeChartBot = function() { console.log('initializeChartBot called before initialization'); };
window.generateSampleChart = function() { console.log('generateSampleChart called before initialization'); };
window.showMarketData = function() { console.log('showMarketData called before initialization'); };
window.exportChatHistory = function() { console.log('exportChatHistory called before initialization'); };
window.clearChatHistory = function() { console.log('clearChatHistory called before initialization'); };
window.showAllCharts = function() { console.log('showAllCharts called before initialization'); };
window.loadChart = function() { console.log('loadChart called before initialization'); };
window.deleteChart = function() { console.log('deleteChart called before initialization'); };
window.saveCurrentChart = function() { console.log('saveCurrentChart called before initialization'); };
window.showNotification = function() { console.log('showNotification called before initialization'); };

console.log('Chart Bot placeholder functions defined');

/**
 * Show notification using the toast system
 */
function showNotification(message, type = 'info', title = '') {
  if (typeof toast !== 'undefined') {
    switch (type) {
      case 'success':
        toast.success(message, title || 'Success');
        break;
      case 'error':
        toast.error(message, title || 'Error');
        break;
      case 'warning':
        toast.warning(message, title || 'Warning');
        break;
      case 'info':
      default:
        toast.info(message, title || 'Information');
        break;
    }
  } else {
    // Fallback to alert if toast is not available
    alert(`${title ? title + ': ' : ''}${message}`);
  }
}

// Immediately replace placeholder
window.showNotification = showNotification;

/**
 * Initialize the chart bot interface
 */
function initializeChartBot() {
  console.log('Initializing Chart Bot...');

  // Check bot service health
  checkBotHealth();

  // Auto-scroll to bottom of chat
  scrollToBottom();

  // Focus on input
  const chatInput = document.getElementById('chat-input');
  if (chatInput) {
    chatInput.focus();
  }

  // Set up event listeners
  setupEventListeners();

  console.log('Chart Bot initialized');
}

// Immediately replace placeholder
window.initializeChartBot = initializeChartBot;

/**
 * Set up all event listeners for the chart bot interface
 */
function setupEventListeners() {
  console.log('Setting up Chart Bot event listeners...');

  // Chat input keypress
  const chatInput = document.getElementById('chat-input');
  if (chatInput) {
    console.log('Chat input found, adding keypress listener');
    // Remove any existing listeners first
    chatInput.removeEventListener('keypress', handleChatKeyPress);
    chatInput.addEventListener('keypress', handleChatKeyPress);
  } else {
    console.error('Chat input element not found!');
  }

  // Send button
  const sendButton = document.getElementById('send-button');
  if (sendButton) {
    console.log('Send button found, adding click listener');
    // Remove any existing listeners first
    sendButton.removeEventListener('click', sendMessage);
    sendButton.addEventListener('click', sendMessage);
  } else {
    console.error('Send button element not found!');
  }

  // Suggestion buttons
  const suggestionButtons = document.querySelectorAll('[data-suggestion]');
  console.log(`Found ${suggestionButtons.length} suggestion buttons`);
  suggestionButtons.forEach(button => {
    button.addEventListener('click', function() {
      const suggestion = this.getAttribute('data-suggestion');
      console.log('Suggestion clicked:', suggestion);
      sendSuggestion(suggestion);
    });
  });

  // Header action buttons
  const clearHistoryBtn = document.getElementById('clear-history-btn');
  if (clearHistoryBtn) {
    clearHistoryBtn.addEventListener('click', clearChatHistory);
  }

  const showChartsBtn = document.getElementById('show-charts-btn');
  if (showChartsBtn) {
    showChartsBtn.addEventListener('click', showSavedCharts);
  }

  // Quick action buttons
  const generateChartBtn = document.getElementById('generate-chart-btn');
  if (generateChartBtn) {
    generateChartBtn.addEventListener('click', generateSampleChart);
  }

  const marketDataBtn = document.getElementById('market-data-btn');
  if (marketDataBtn) {
    marketDataBtn.addEventListener('click', showMarketData);
  }

  const exportHistoryBtn = document.getElementById('export-history-btn');
  if (exportHistoryBtn) {
    exportHistoryBtn.addEventListener('click', exportChatHistory);
  }

  // Chart action buttons
  const loadChartBtns = document.querySelectorAll('.load-chart-btn');
  loadChartBtns.forEach(button => {
    button.addEventListener('click', function() {
      const chartId = this.getAttribute('data-chart-id');
      loadChart(chartId);
    });
  });

  const deleteChartBtns = document.querySelectorAll('.delete-chart-btn');
  deleteChartBtns.forEach(button => {
    button.addEventListener('click', function() {
      const chartId = this.getAttribute('data-chart-id');
      deleteChart(chartId);
    });
  });

  // Show all charts button
  const showAllChartsBtn = document.getElementById('show-all-charts-btn');
  if (showAllChartsBtn) {
    showAllChartsBtn.addEventListener('click', showAllCharts);
  }

  // Save chart button (in modal)
  const saveChartBtn = document.getElementById('save-chart-btn');
  if (saveChartBtn) {
    saveChartBtn.addEventListener('click', saveCurrentChart);
  }

  console.log('Chart Bot event listeners set up successfully');
}

/**
 * Check the health of the chart bot service
 */
async function checkBotHealth() {
  try {
    const response = await fetch('/chart-bot/api/health');
    const data = await response.json();

    const statusElement = document.getElementById('bot-status');
    if (statusElement) {
      if (data.success && data.chartBotStatus === 'healthy') {
        statusElement.innerHTML = `
          <span class="badge bg-success">
            <i class="bi bi-database-check"></i> Vector Store Online
          </span>
        `;
        chartBotStatus = 'healthy';

        // Show success notification
        showNotification(
          'Agricultural knowledge base is online and ready',
          'success',
          'Vector Store Connected'
        );
      } else {
        statusElement.innerHTML = `
          <span class="badge bg-warning">
            <i class="bi bi-database-exclamation"></i> Limited Access
          </span>
        `;
        chartBotStatus = 'limited';

        // Show warning notification
        showNotification(
          'Knowledge base access is limited. Some features may not work properly.',
          'warning',
          'Vector Store Warning'
        );
      }
    }
  } catch (error) {
    console.error('Error checking bot health:', error);
    const statusElement = document.getElementById('bot-status');
    if (statusElement) {
      statusElement.innerHTML = `
        <span class="badge bg-danger">
          <i class="bi bi-database-x"></i> Vector Store Offline
        </span>
      `;
    }
    chartBotStatus = 'offline';

    // Show error notification
    showNotification(
      'Unable to connect to the agricultural knowledge base. Please check if the service is running.',
      'error',
      'Vector Store Error'
    );
  }
}

/**
 * Handle chat input key press
 */
function handleChatKeyPress(event) {
  if (event.key === 'Enter' && !event.shiftKey) {
    event.preventDefault();
    sendMessage();
  }
}

// Immediately replace placeholder
window.handleChatKeyPress = handleChatKeyPress;

/**
 * Send a message to the chat bot
 */
async function sendMessage() {
  console.log('sendMessage function called');
  const chatInput = document.getElementById('chat-input');
  const sendButton = document.getElementById('send-button');

  if (!chatInput || !sendButton) {
    console.error('Chat input or send button not found');
    showNotification('Chat interface elements not found. Please refresh the page.', 'error');
    return;
  }

  const message = chatInput.value.trim();
  console.log('Message to send:', message);
  if (!message) {
    console.log('Empty message, not sending');
    return;
  }

  // Disable input and show loading
  chatInput.disabled = true;
  sendButton.disabled = true;
  sendButton.innerHTML = '<i class="bi bi-arrow-clockwise spin"></i>';

  const startTime = Date.now();
  let retrievalMessageElement = null;

  try {
    // Add user message to chat
    addMessageToChat(message, 'user');

    // Clear input
    chatInput.value = '';

    // Show vector store retrieval indicator
    const chatMessages = document.getElementById('chat-messages');
    if (chatMessages) {
      retrievalMessageElement = document.createElement('div');
      retrievalMessageElement.className = 'chat-message bot-message processing-message';
      retrievalMessageElement.innerHTML = `
        <div class="message-avatar">
          <i class="bi bi-robot"></i>
        </div>
        <div class="message-content">
          <div class="vector-store-indicator">
            <small class="text-info">
              <i class="bi bi-search"></i>
              Searching agricultural knowledge base...
            </small>
          </div>
          <div class="message-text">
            <div class="spinner-border spinner-border-sm text-primary" role="status">
              <span class="visually-hidden">Loading...</span>
            </div>
            Retrieving relevant information from vector store...
          </div>
        </div>
      `;
      chatMessages.appendChild(retrievalMessageElement);
      scrollToBottom();
    }

    // Send to API
    console.log('Sending request to /chart-bot/api/chat');
    const response = await fetch('/chart-bot/api/chat', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query: message,
        maxSources: 5
      })
    });

    console.log('Response status:', response.status);

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    console.log('Response data:', data);

    if (data.success) {
      // Remove the processing message
      if (retrievalMessageElement && retrievalMessageElement.parentNode) {
        retrievalMessageElement.parentNode.removeChild(retrievalMessageElement);
      }

      // Add bot response to chat with enhanced source display
      addMessageToChat(data.response, 'bot', data.sources, {
        status: data.status,
        query: data.query,
        retrievalTime: Date.now() - startTime
      });

      // Show notification about vector store retrieval
      if (data.sources && data.sources.length > 0) {
        showNotification(
          `Retrieved information from ${data.sources.length} knowledge source(s)`,
          'success',
          'Vector Store Query'
        );
      }

      // Check if response contains chart data or visualization request
      if (shouldGenerateChart(message, data.response)) {
        generateChartFromResponse(data.response, message);
      }
    } else {
      // Remove the processing message
      if (retrievalMessageElement && retrievalMessageElement.parentNode) {
        retrievalMessageElement.parentNode.removeChild(retrievalMessageElement);
      }

      addMessageToChat(
        'Sorry, I encountered an error processing your request. Please try again.',
        'bot',
        [],
        { status: 'error' }
      );
    }

  } catch (error) {
    console.error('Error sending message:', error);

    // Remove the processing message
    if (retrievalMessageElement && retrievalMessageElement.parentNode) {
      retrievalMessageElement.parentNode.removeChild(retrievalMessageElement);
    }

    let errorMessage = 'Sorry, I\'m having trouble connecting to the knowledge base right now. Please try again later.';
    let notificationMessage = 'Unable to connect to the agricultural knowledge base';

    // Check if it's an authentication error
    if (error.message.includes('401') || error.message.includes('403')) {
      errorMessage = 'Please log in to use the AI assistant.';
      notificationMessage = 'Authentication required';
    } else if (error.message.includes('404')) {
      errorMessage = 'AI service not found. Please contact support.';
      notificationMessage = 'Service not available';
    }

    addMessageToChat(
      errorMessage,
      'bot',
      [],
      { status: 'error' }
    );

    showNotification(
      notificationMessage,
      'error',
      'Connection Error'
    );
  } finally {
    // Re-enable input
    chatInput.disabled = false;
    sendButton.disabled = false;
    sendButton.innerHTML = '<i class="bi bi-send"></i>';
    chatInput.focus();
  }
}

// Immediately replace placeholder
window.sendMessage = sendMessage;

/**
 * Send a suggested message
 */
function sendSuggestion(suggestion) {
  const chatInput = document.getElementById('chat-input');
  if (chatInput) {
    chatInput.value = suggestion;
    sendMessage();
  }
}

// Immediately replace placeholder
window.sendSuggestion = sendSuggestion;

/**
 * Add a message to the chat interface
 */
function addMessageToChat(message, sender, sources = [], metadata = {}) {
  const chatMessages = document.getElementById('chat-messages');
  if (!chatMessages) {
    console.error('Chat messages container not found');
    return;
  }

  const messageDiv = document.createElement('div');
  messageDiv.className = `message ${sender}-message`;

  const contentDiv = document.createElement('div');
  contentDiv.className = 'message-content';

  // Add the message text
  contentDiv.innerHTML = formatMessageText(message);

  // Add sources for bot messages (Chart GPT style)
  if (sender === 'bot' && sources && sources.length > 0) {
    const sourcesDiv = document.createElement('div');
    sourcesDiv.className = 'message-sources';

    sources.forEach(source => {
      const sourceTag = document.createElement('span');
      sourceTag.className = 'source-tag';
      sourceTag.textContent = source.title || 'Agricultural Knowledge';
      sourcesDiv.appendChild(sourceTag);
    });

    contentDiv.appendChild(sourcesDiv);
  }

  messageDiv.appendChild(contentDiv);
  chatMessages.appendChild(messageDiv);

  // Scroll to bottom
  scrollToBottom();

  // Check if we should generate a chart
  if (sender === 'bot') {
    // For Chart GPT style, show charts in the main display area
    const shouldChart = shouldGenerateChart(message, message);
    if (shouldChart) {
      const chartData = generateSampleChartData(message);
      if (chartData) {
        showChartInDisplay(chartData);
      }
    }
  }



/**
 * Format message text to handle line breaks and basic formatting
 */
function formatMessageText(text) {
  return text
    .replace(/\n/g, '<br>')
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    .replace(/\*(.*?)\*/g, '<em>$1</em>');
}

/**
 * Scroll chat to bottom
 */
function scrollToBottom() {
  const chatMessages = document.getElementById('chat-messages');
  if (chatMessages) {
    chatMessages.scrollTop = chatMessages.scrollHeight;
  }
}

/**
 * Check if a chart should be generated based on the message and response
 */
function shouldGenerateChart(userMessage, botResponse) {
  const chartKeywords = [
    'chart', 'graph', 'plot', 'visualize', 'show data', 'trend', 'price',
    'statistics', 'analysis', 'compare', 'growth', 'yield', 'production'
  ];
  
  const combinedText = (userMessage + ' ' + botResponse).toLowerCase();
  return chartKeywords.some(keyword => combinedText.includes(keyword));
}

/**
 * Generate a chart from the bot response
 */
function generateChartFromResponse(response, userQuery) {
  // This is a simplified chart generation
  // In a real implementation, you would parse the response for data
  const sampleData = generateSampleChartData(userQuery);
  
  if (sampleData) {
    showChartModal(sampleData);
  }
}

/**
 * Generate sample chart data based on user query
 */
function generateSampleChartData(query) {
  const lowerQuery = query.toLowerCase();
  
  if (lowerQuery.includes('corn') || lowerQuery.includes('maize')) {
    return {
      type: 'line',
      title: 'Corn Price Trends',
      data: {
        labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
        datasets: [{
          label: 'Corn Price ($/bushel)',
          data: [4.2, 4.5, 4.8, 4.3, 4.6, 4.9],
          borderColor: '#4CAF50',
          backgroundColor: 'rgba(76, 175, 80, 0.1)',
          tension: 0.3
        }]
      }
    };
  } else if (lowerQuery.includes('tomato')) {
    return {
      type: 'bar',
      title: 'Tomato Yield by Month',
      data: {
        labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
        datasets: [{
          label: 'Yield (tons/hectare)',
          data: [12, 15, 18, 22, 25, 20],
          backgroundColor: '#FF6384',
          borderColor: '#FF6384',
          borderWidth: 1
        }]
      }
    };
  } else if (lowerQuery.includes('weather') || lowerQuery.includes('temperature')) {
    return {
      type: 'line',
      title: 'Temperature Trends',
      data: {
        labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4'],
        datasets: [{
          label: 'Temperature (°C)',
          data: [22, 25, 28, 24],
          borderColor: '#FF9800',
          backgroundColor: 'rgba(255, 152, 0, 0.1)',
          tension: 0.3
        }]
      }
    };
  }
  
  // Default chart
  return {
    type: 'doughnut',
    title: 'Agricultural Data Overview',
    data: {
      labels: ['Crops', 'Livestock', 'Equipment', 'Land'],
      datasets: [{
        data: [40, 25, 20, 15],
        backgroundColor: ['#4CAF50', '#2196F3', '#FF9800', '#9C27B0']
      }]
    }
  };
}

/**
 * Show chart in modal (Chart GPT style)
 */
function showChartModal(chartData) {
  const modal = document.getElementById('chartModal');
  const container = document.getElementById('chart-container');

  if (!container) {
    console.error('Chart container not found');
    return;
  }

  // Clear previous chart
  container.innerHTML = '<canvas id="chart-canvas"></canvas>';

  const canvas = document.getElementById('chart-canvas');
  if (!canvas) {
    console.error('Chart canvas not found');
    return;
  }

  // Create chart
  currentChart = new Chart(canvas, {
    type: chartData.type,
    data: chartData.data,
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        title: {
          display: true,
          text: chartData.title
        }
      }
    }
  });

  // Show modal (Chart GPT style)
  if (modal) {
    modal.style.display = 'flex';
  }
}

/**
 * Show chart in main display area (Chart GPT style)
 */
function showChartInDisplay(chartData) {
  const displayArea = document.getElementById('chart-display');

  if (!displayArea) {
    console.error('Chart display area not found');
    return;
  }

  // Clear previous content
  displayArea.innerHTML = '<canvas id="main-chart-canvas"></canvas>';
  displayArea.classList.add('has-chart');

  const canvas = document.getElementById('main-chart-canvas');
  if (!canvas) {
    console.error('Main chart canvas not found');
    return;
  }

  // Create chart
  currentChart = new Chart(canvas, {
    type: chartData.type,
    data: chartData.data,
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        title: {
          display: true,
          text: chartData.title,
          font: {
            size: 16,
            weight: 'bold'
          }
        }
      }
    }
  });
}

/**
 * Save the current chart
 */
async function saveCurrentChart() {
  if (!currentChart) {
    showNotification('No chart to save', 'warning');
    return;
  }
  
  const title = prompt('Enter a title for this chart:');
  if (!title) {
    return;
  }
  
  const description = prompt('Enter a description (optional):') || '';
  
  try {
    const response = await fetch('/chart-bot/api/save-chart', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        title: title,
        description: description,
        chartData: currentChart.data,
        chartConfig: currentChart.config,
        chartType: currentChart.config.type
      })
    });
    
    const data = await response.json();
    
    if (data.success) {
      showNotification('Chart saved successfully!', 'success');
      // Refresh saved charts list
      loadSavedCharts();
    } else {
      showNotification('Failed to save chart', 'error');
    }
  } catch (error) {
    console.error('Error saving chart:', error);
    showNotification('Error saving chart', 'error');
  }
}

/**
 * Generate a sample chart for demonstration (Chart GPT style)
 */
function generateSampleChart() {
  const sampleData = {
    type: 'bar',
    title: 'Sample Agricultural Data',
    data: {
      labels: ['Wheat', 'Corn', 'Rice', 'Soybeans', 'Barley'],
      datasets: [{
        label: 'Production (Million Tons)',
        data: [765, 1134, 756, 341, 156],
        backgroundColor: [
          '#4CAF50',
          '#2196F3',
          '#FF9800',
          '#9C27B0',
          '#F44336'
        ]
      }]
    }
  };

  // Show in main display area (Chart GPT style)
  showChartInDisplay(sampleData);

  // Also add a message about the chart
  addMessageToChat('Here\'s a sample chart showing global agricultural production data. This demonstrates how data can be visualized in our Chart GPT-style interface.', 'bot');
}

/**
 * Show market data
 */
function showMarketData() {
  window.open('/market-trends', '_blank');
}

/**
 * Export chat history
 */
async function exportChatHistory() {
  try {
    const response = await fetch('/chart-bot/api/chat-history?limit=100');
    const data = await response.json();
    
    if (data.success) {
      const chatData = data.history.map(chat => ({
        timestamp: new Date(chat.timestamp?.toDate ? chat.timestamp.toDate() : chat.timestamp).toISOString(),
        userMessage: chat.userMessage,
        botResponse: chat.botResponse,
        sources: chat.sources?.length || 0
      }));
      
      const blob = new Blob([JSON.stringify(chatData, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `chat-history-${new Date().toISOString().split('T')[0]}.json`;
      a.click();
      URL.revokeObjectURL(url);
      
      showNotification('Chat history exported successfully!', 'success');
    } else {
      showNotification('Failed to export chat history', 'error');
    }
  } catch (error) {
    console.error('Error exporting chat history:', error);
    showNotification('Error exporting chat history', 'error');
  }
}

/**
 * Clear chat history
 */
async function clearChatHistory() {
  if (!confirm('Are you sure you want to clear your chat history? This action cannot be undone.')) {
    return;
  }
  
  try {
    // Clear the UI
    const chatMessages = document.getElementById('chat-messages');
    if (chatMessages) {
      // Keep only the welcome message
      const welcomeMessage = chatMessages.querySelector('.bot-message');
      chatMessages.innerHTML = '';
      if (welcomeMessage) {
        chatMessages.appendChild(welcomeMessage);
      }
    }
    
    showNotification('Chat history cleared!', 'success');
  } catch (error) {
    console.error('Error clearing chat history:', error);
    showNotification('Error clearing chat history', 'error');
  }
}

/**
 * Load saved charts
 */
async function loadSavedCharts() {
  try {
    const response = await fetch('/chart-bot/api/charts');
    const data = await response.json();
    
    if (data.success) {
      const chartsList = document.getElementById('saved-charts-list');
      if (chartsList && data.charts.length > 0) {
        // Update the charts list
        // This would require updating the DOM structure
        console.log('Loaded charts:', data.charts);
      }
    }
  } catch (error) {
    console.error('Error loading saved charts:', error);
  }
}

/**
 * Show all saved charts
 */
function showAllCharts() {
  showNotification('Showing all charts feature coming soon!', 'info');
}

/**
 * Show saved charts modal
 */
function showSavedCharts() {
  showNotification('Saved charts modal coming soon!', 'info');
}

// Immediately replace placeholder
window.showSavedCharts = showSavedCharts;

/**
 * Load a specific chart
 */
function loadChart(chartId) {
  showNotification(`Loading chart ${chartId}...`, 'info');
}

/**
 * Delete a chart
 */
async function deleteChart(chartId) {
  if (!confirm('Are you sure you want to delete this chart?')) {
    return;
  }
  
  try {
    const response = await fetch(`/chart-bot/api/charts/${chartId}`, {
      method: 'DELETE'
    });
    
    const data = await response.json();
    
    if (data.success) {
      showNotification('Chart deleted successfully!', 'success');
      // Remove from UI
      const chartElement = document.querySelector(`[data-chart-id="${chartId}"]`);
      if (chartElement) {
        chartElement.remove();
      }
    } else {
      showNotification('Failed to delete chart', 'error');
    }
  } catch (error) {
    console.error('Error deleting chart:', error);
    showNotification('Error deleting chart', 'error');
  }
}

// Make functions available globally with error handling
try {
  window.initializeChartBot = initializeChartBot;
  window.setupEventListeners = setupEventListeners;
  window.handleChatKeyPress = handleChatKeyPress;
  window.sendMessage = sendMessage;
  window.sendSuggestion = sendSuggestion;
  window.generateSampleChart = generateSampleChart;
  window.showMarketData = showMarketData;
  window.exportChatHistory = exportChatHistory;
  window.clearChatHistory = clearChatHistory;
  window.showSavedCharts = showSavedCharts;
  window.showAllCharts = showAllCharts;
  window.loadChart = loadChart;
  window.deleteChart = deleteChart;
  window.saveCurrentChart = saveCurrentChart;
  window.showNotification = showNotification;

  console.log('Chart Bot functions loaded successfully');
} catch (error) {
  console.error('Error loading Chart Bot functions:', error);
}

// Ensure functions are available immediately
if (typeof window !== 'undefined') {
  // Double-check that all functions are properly attached
  const requiredFunctions = [
    'initializeChartBot', 'handleChatKeyPress', 'sendMessage', 'sendSuggestion',
    'generateSampleChart', 'showMarketData', 'exportChatHistory', 'clearChatHistory',
    'showSavedCharts', 'showAllCharts', 'loadChart', 'deleteChart', 'saveCurrentChart'
  ];

  requiredFunctions.forEach(funcName => {
    if (typeof window[funcName] !== 'function') {
      console.error(`Function ${funcName} is not properly attached to window`);
    }
  });
}

// Additional safety check - ensure functions are available when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
  console.log('Chart Bot script DOM ready event');

  // Re-attach functions if they're missing
  const functionsToAttach = {
    sendMessage,
    sendSuggestion,
    handleChatKeyPress,
    showSavedCharts,
    initializeChartBot,
    setupEventListeners,
    generateSampleChart,
    showMarketData,
    exportChatHistory,
    clearChatHistory,
    showAllCharts,
    loadChart,
    deleteChart,
    saveCurrentChart,
    showNotification
  };

  Object.entries(functionsToAttach).forEach(([name, func]) => {
    if (typeof window[name] !== 'function') {
      window[name] = func;
      console.log(`Attached function: ${name}`);
    }
  });

  console.log('Chart Bot DOM ready - functions verified and attached');

  // Force setup event listeners if not already done
  setTimeout(() => {
    const chatInput = document.getElementById('chat-input');
    const sendButton = document.getElementById('send-button');

    if (chatInput && sendButton) {
      console.log('DOM ready: Elements found, checking event listeners...');

      // Remove any existing listeners and add fresh ones
      const newChatInput = chatInput.cloneNode(true);
      const newSendButton = sendButton.cloneNode(true);

      chatInput.parentNode.replaceChild(newChatInput, chatInput);
      sendButton.parentNode.replaceChild(newSendButton, sendButton);

      // Add fresh event listeners
      newChatInput.addEventListener('keypress', function(event) {
        console.log('Keypress detected:', event.key);
        if (event.key === 'Enter' && !event.shiftKey) {
          event.preventDefault();
          console.log('Enter key pressed, calling sendMessage...');
          if (typeof window.sendMessage === 'function') {
            window.sendMessage();
          } else {
            console.error('sendMessage function not available!');
          }
        }
      });

      newSendButton.addEventListener('click', function() {
        console.log('Send button clicked, calling sendMessage...');
        if (typeof window.sendMessage === 'function') {
          window.sendMessage();
        } else {
          console.error('sendMessage function not available!');
        }
      });

      console.log('Fresh event listeners attached successfully');
    } else {
      console.error('DOM ready: Elements not found!');
    }
  }, 500);
});

// Immediate function attachment (backup)
(function() {
  const functionsToAttach = {
    sendMessage,
    sendSuggestion,
    handleChatKeyPress,
    showSavedCharts,
    initializeChartBot,
    setupEventListeners,
    generateSampleChart,
    showMarketData,
    exportChatHistory,
    clearChatHistory,
    showAllCharts,
    loadChart,
    deleteChart,
    saveCurrentChart,
    showNotification
  };

  Object.entries(functionsToAttach).forEach(([name, func]) => {
    window[name] = func;
  });

  console.log('Chart Bot functions attached immediately');
})();
